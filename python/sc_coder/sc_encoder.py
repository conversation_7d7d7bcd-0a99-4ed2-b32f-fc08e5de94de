#!/usr/bin/env python
# -*- coding: utf-8 -*-
#
# Copyright 2025 gr-sc_coder author.
#
# SPDX-License-Identifier: GPL-3.0-or-later
#


import numpy as np
import onnxruntime
import pmt
import torch
from gnuradio import gr
from reedsolo import RSCodec
from torchvision import datasets, transforms
from torchvision.utils import make_grid, save_image


def get_data(batch_size, shuffle=True, n_worker=0, train=True, add_noise=0):
    transform = transforms.Compose(
        [
            transforms.ToTensor(),
        ]
    )
    batch_size = 1
    train_dataset = datasets.ImageFolder(
        "/home/<USER>/USRP_SFDMA/sfdma_bc_recon/CelebA64", transform=transform
    )
    test_dataset = train_dataset

    test_loader1 = torch.utils.data.DataLoader(
        test_dataset, batch_size=batch_size, shuffle=True
    )

    test_loader2 = torch.utils.data.DataLoader(
        test_dataset, batch_size=batch_size, shuffle=True
    )
    return test_loader1, test_loader2


class sc_encoder(gr.sync_block):
    """
    docstring for block sc_encoder
    """

    def __init__(
        self, ckpt_path="/home/<USER>/USRP_SFDMA/sfdma_bc_recon/transmitter_model.onnx"
    ):
        gr.sync_block.__init__(self, name="sc_encoder", in_sig=None, out_sig=None)
        self.dataloader1, self.dataloader2 = get_data(32, n_worker=4, train=False)
        self.onnx_session = onnxruntime.InferenceSession(ckpt_path)
        """量化器"""
        self.idx = 0
        RS_N = 6  # 总符号数
        RS_K = 2  # 数据符号数 (self.idx 占 2 字节)

        self.rsc = RSCodec(RS_N - RS_K)  # 4 个冗余字节
        self.message_port_register_in(pmt.intern("PDU_in"))
        self.message_port_register_out(pmt.intern("PDU_out"))
        self.set_msg_handler(pmt.intern("PDU_in"), self.handle_msg)

    def handle_msg(self, msg):
        imgs1 = next(iter(self.dataloader1))[0]
        imgs2 = next(iter(self.dataloader2))[0]

        grid1 = make_grid(imgs1, nrow=8, padding=2, normalize=True)
        grid2 = make_grid(imgs2, nrow=8, padding=2, normalize=True)
        save_image(grid1, f"./data/imgs1_{self.idx}.jpg")
        save_image(grid2, f"./data/imgs2_{self.idx}.jpg")

        # 保存原始形状
        original_shape1 = imgs1.shape
        original_shape2 = imgs2.shape

        # 分别对 imgs1 和 imgs2 交换 dim0 和 dim1 (transpose)
        imgs1 = imgs1.transpose(0, 1)  # 交换 dim0 和 dim1
        imgs2 = imgs2.transpose(0, 1)  # 交换 dim0 和 dim1

        # reshape 回原来的形状
        imgs1 = imgs1.reshape(original_shape1)
        imgs2 = imgs2.reshape(original_shape2)

        imgs1 = imgs1.numpy().astype(np.float32)
        imgs2 = imgs2.numpy().astype(np.float32)

        onnx_inputs = {"input1": imgs1, "input2": imgs2}

        quantized_feature = self.onnx_session.run(None, onnx_inputs)[0]
        quantized_feature = quantized_feature * 0.5 + 0.5
        quantized_feature = quantized_feature.reshape(1, -1).astype(np.uint8)

        x_byte = np.packbits(quantized_feature)
        x_byte_vector = x_byte.reshape(1, -1).squeeze()
        encoded_idx = self.encode_idx(self.idx)
        x_byte_vector = np.insert(x_byte_vector, 0, list(encoded_idx))
        print(x_byte_vector.shape)
        self.idx += 1
        x_pmt = pmt.to_pmt(x_byte_vector)
        x_pdu = pmt.cons(pmt.PMT_NIL, x_pmt)
        self.message_port_pub(pmt.intern("PDU_out"), x_pdu)
