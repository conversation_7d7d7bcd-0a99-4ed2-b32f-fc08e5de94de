# Copyright 2011 Free Software Foundation, Inc.
#
# This file was generated by gr_modtool, a tool from the GNU Radio framework
# This file is a part of gr-sc_coder
#
# SPDX-License-Identifier: GPL-3.0-or-later
#

########################################################################
# Include python install macros
########################################################################
include(GrPython)
if(NOT PYTHONINTERP_FOUND)
    return()
endif()

add_subdirectory(bindings)

########################################################################
# Install python sources
########################################################################
gr_python_install(FILES __init__.py
    sc_encoder.py DESTINATION ${GR_PYTHON_DIR}/gnuradio/sc_coder)

########################################################################
# Handle the unit tests
########################################################################
include(GrTest)

set(GR_TEST_TARGET_DEPS gnuradio-sc_coder)

# Create a package directory that tests can import. It includes everything
# from `python/`.
add_custom_target(
    copy_module_for_tests ALL
    COMMAND ${CMAKE_COMMAND} -E copy_directory ${CMAKE_CURRENT_SOURCE_DIR}
            ${PROJECT_BINARY_DIR}/test_modules/gnuradio/sc_coder/)
