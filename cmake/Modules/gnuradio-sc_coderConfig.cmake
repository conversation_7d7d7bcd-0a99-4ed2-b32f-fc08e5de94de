find_package(PkgConfig)

PKG_CHECK_MODULES(PC_GR_SC_CODER gnuradio-sc_coder)

FIND_PATH(
    GR_SC_CODER_INCLUDE_DIRS
    NAMES gnuradio/sc_coder/api.h
    HINTS $ENV{SC_CODER_DIR}/include
        ${PC_SC_CODER_INCLUDEDIR}
    PATHS ${CMAKE_INSTALL_PREFIX}/include
          /usr/local/include
          /usr/include
)

FIND_LIBRARY(
    GR_SC_CODER_LIBRARIES
    NAMES gnuradio-sc_coder
    HINTS $ENV{SC_CODER_DIR}/lib
        ${PC_SC_CODER_LIBDIR}
    PATHS ${CMAKE_INSTALL_PREFIX}/lib
          ${CMAKE_INSTALL_PREFIX}/lib64
          /usr/local/lib
          /usr/local/lib64
          /usr/lib
          /usr/lib64
          )

include("${CMAKE_CURRENT_LIST_DIR}/gnuradio-sc_coderTarget.cmake")

INCLUDE(FindPackageHandleStandardArgs)
FIND_PACKAGE_HANDLE_STANDARD_ARGS(GR_SC_CODER DEFAULT_MSG GR_SC_CODER_LIBRARIES GR_SC_CODER_INCLUDE_DIRS)
MARK_AS_ADVANCED(GR_SC_CODER_LIBRARIES GR_SC_CODER_INCLUDE_DIRS)
