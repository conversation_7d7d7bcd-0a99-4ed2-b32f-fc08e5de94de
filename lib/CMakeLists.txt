# Copyright 2011,2012,2016,2018,2019 Free Software Foundation, Inc.
#
# This file was generated by gr_modtool, a tool from the GNU Radio framework
# This file is a part of gr-sc_coder
#
# SPDX-License-Identifier: GPL-3.0-or-later
#

########################################################################
# Setup library
########################################################################
include(GrPlatform) #define LIB_SUFFIX

list(APPEND sc_coder_sources)

set(sc_coder_sources
    "${sc_coder_sources}"
    PARENT_SCOPE)
if(NOT sc_coder_sources)
    message(STATUS "No C++ sources... skipping lib/")
    return()
endif(NOT sc_coder_sources)

add_library(gnuradio-sc_coder SHARED ${sc_coder_sources})
target_link_libraries(gnuradio-sc_coder gnuradio::gnuradio-runtime)
target_include_directories(
    gnuradio-sc_coder
    PUBLIC $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/../include>
    PUBLIC $<INSTALL_INTERFACE:include>)
set_target_properties(gnuradio-sc_coder PROPERTIES DEFINE_SYMBOL "gnuradio_sc_coder_EXPORTS")

if(APPLE)
    set_target_properties(gnuradio-sc_coder PROPERTIES INSTALL_NAME_DIR
                                                    "${CMAKE_INSTALL_PREFIX}/lib")
endif(APPLE)

########################################################################
# Install built library files
########################################################################
include(GrMiscUtils)
gr_library_foo(gnuradio-sc_coder)

########################################################################
# Print summary
########################################################################
message(STATUS "Using install prefix: ${CMAKE_INSTALL_PREFIX}")
message(STATUS "Building for version: ${VERSION} / ${LIBVER}")

########################################################################
# Build and register unit test
########################################################################
include(GrTest)

# If your unit tests require special include paths, add them here
#include_directories()
# List all files that contain Boost.UTF unit tests here
list(APPEND test_sc_coder_sources)
# Anything we need to link to for the unit tests go here
list(APPEND GR_TEST_TARGET_DEPS gnuradio-sc_coder)

if(NOT test_sc_coder_sources)
    message(STATUS "No C++ unit tests... skipping")
    return()
endif(NOT test_sc_coder_sources)

foreach(qa_file ${test_sc_coder_sources})
    gr_add_cpp_test("sc_coder_${qa_file}" ${CMAKE_CURRENT_SOURCE_DIR}/${qa_file})
endforeach(qa_file)
